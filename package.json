{"name": "sw-ubo-hub-bridge", "version": "0.2.93", "scripts": {"config": "scripty", "prebuild": "rimraf ./dist", "start": "env-cmd ng serve", "build": "ng build", "build:prod": "ng build --configuration production --source-map", "test": "ng test", "test:coverage": "ng test --no-watch --no-progress --code-coverage", "lint": "ng lint", "e2e": "ng e2e", "postinstall": "ngcc"}, "private": true, "dependencies": {"@angular/animations": "12.2.5", "@angular/cdk": "12.2.5", "@angular/common": "12.2.5", "@angular/compiler": "12.2.5", "@angular/core": "12.2.5", "@angular/flex-layout": "12.0.0-beta.34", "@angular/forms": "12.2.5", "@angular/material": "12.2.5", "@angular/platform-browser": "12.2.5", "@angular/platform-browser-dynamic": "12.2.5", "@angular/router": "12.2.5", "@auth0/angular-jwt": "5.0.2", "@ngx-formly/core": "5.10.3", "@ngx-formly/material": "5.10.3", "@ngx-formly/schematics": "5.10.3", "@ngx-translate/core": "13.0.0", "@ngx-translate/http-loader": "6.0.0", "@skywind-group/lib-swui": "^0.1.649", "dexie": "3.0.3", "moment": "2.29.1", "moment-timezone": "0.5.32", "ngx-color-picker": "10.1.0", "rxjs": "6.6.3", "tslib": "2.0.0", "zone.js": "0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "12.2.5", "@angular/cli": "12.2.5", "@angular/compiler-cli": "12.2.5", "@types/jasmine": "3.6.0", "@types/jasminewd2": "2.0.8", "@types/node": "14.11.1", "codelyzer": "6.0.0", "env-cmd": "10.1.0", "jasmine-core": "3.6.0", "jasmine-spec-reporter": "5.0.0", "karma": "6.3.4", "karma-chrome-launcher": "3.1.0", "karma-coverage-istanbul-reporter": "3.0.3", "karma-jasmine": "4.0.0", "karma-jasmine-html-reporter": "1.5.0", "protractor": "7.0.0", "rimraf": "3.0.2", "rxjs-tslint": "0.1.8", "scripty": "2.0.0", "ts-node": "9.0.0", "tslint": "6.1.3", "typescript": "4.3.5"}}
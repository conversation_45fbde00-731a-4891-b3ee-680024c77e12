services:

  sw-ubo-hub-bridge:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - 3002:80
    environment:
      - MAPI=api-bo.gms-server.dev-qa.ss211208.com:80 # api:3000
      - LOGIN_URL=http://api.cd.d.skywind-tech.com:8880/auth/login
      - CASINO_HUB_URL=http://api.cd.d.skywind-tech.com:8881
      - DATA_HUB_URL=http://api.cd.d.skywind-tech.com:8881/bi
      - ENGAGEMENT_HUB_URL=http://api.cd.d.skywind-tech.com:8882
      - SOFTGATE_HOST=localhost
      - SOFTGATE_BRIDGE_URL=localhost
      - SOFTGATE_LOGIN_URL=localhost
      - SOFTGATE_CASINO_HUB_URL=localhost
      - SOFTGATE_ENGAGEMENT_HUB_URL=localhost
      - SOFTGATE_DATA_HUB_URL=localhost
      - SOFTGATE_STUDIO_HUB_URL=localhost

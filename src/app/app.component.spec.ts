import { TestBed, waitForAsync } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { StorageService } from './storage.service';
import { MapiService } from './mapi.service';
import { TranslateModule } from '@ngx-translate/core';
import { RefreshTokenService } from './refresh-token.service';

describe('AppComponent', () => {
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        TranslateModule.forRoot()
      ],
      declarations: [
        AppComponent
      ],
      providers: [
        StorageService,
        { provide: MapiService, useValue: {} },
        { provide: RefreshTokenService, useValue: {} }
      ]
    }).compileComponents();
  }));

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });
});

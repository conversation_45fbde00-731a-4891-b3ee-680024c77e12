import { Injectable } from '@angular/core';
import { LoginInfo, MapiService } from './mapi.service';
import { Observable, of, Subject, timer } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { JwtHelperService } from '@auth0/angular-jwt';

interface TokenData {
  token: string | null;
  timeout: number | null;
  permissions: string | null;
}

interface AuthData {
  token: string | null;
  permissions: string | null;
}

@Injectable()
export class RefreshTokenService {
  readonly token$: Observable<AuthData>;

  private readonly accessToken$ = new Subject<AuthData>();
  private readonly jwt = new JwtHelperService();

  constructor( private readonly mapiService: MapiService ) {
    this.token$ = this.accessToken$.pipe(
      map<AuthData, TokenData>(( { token, permissions } ) => ({ token, permissions, timeout: this.timeout(token) })),
      switchMap(( { timeout, token } ) => {
        if (timeout) {
          console.log(`Set refresh token at ${new Date(new Date().getTime() + timeout)}`);
          return timer(timeout).pipe(
            switchMap(() => this.mapiService.refreshToken(token)),
            map<LoginInfo, AuthData>(( { accessToken, grantedPermissions } ) => ({ token: accessToken, permissions: grantedPermissions }))
          );
        }
        return of({ token: null, permissions: null });
      })
    );
  }

  setToken( token: string | null, permissions: string | null ): void {
    if (token) {
      this.accessToken$.next({ token, permissions });
    }
  }

  private timeout( token: string ): number | null {
    try {
      const date = this.jwt.getTokenExpirationDate(token);
      const timeout = date ? date.getTime() - Date.now() - 60000 : null;
      return timeout && timeout > 0 ? timeout : null;
    } catch (e) {
      console.warn(e);
    }
    return null;
  }
}

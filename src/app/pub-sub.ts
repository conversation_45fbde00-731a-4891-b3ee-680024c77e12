type ListenerFn<T> = ( data: T | null ) => void;

export class PubSub<T = any> {
  private readonly channel: string;

  constructor( channel: string, listener?: ListenerFn<T> ) {
    this.channel = `ps-channel-${channel}`;
    if (listener) {
      this.subscribe(listener);
    }
  }

  publish( data: T | null ) {
    localStorage.setItem(this.channel, this.encode(data));
    this.listener(data);
  }

  get(): T | null {
    return this.decode(localStorage.getItem(this.channel));
  }

  subscribe( listener: ListenerFn<T> ) {
    this.listener = listener;
    window.addEventListener('storage', e => {
      if (e.key === this.channel) {
        this.listener(this.decode(e.newValue));
      }
    }, false);
  }

  private encode( data: T | null ): string {
    return JSON.stringify([data, Date.now()]);
  }

  private decode( data: string | null ): T | null {
    return data ? JSON.parse(data)[0] : null;
  }

  private listener: ListenerFn<T> = () => {
  }
}

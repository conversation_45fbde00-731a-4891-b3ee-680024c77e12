import { AfterViewInit, Component, OnDestroy, OnInit } from '@angular/core';
import { PubSub } from './pub-sub';
import { from, fromEvent, of, Subject, timer } from 'rxjs';
import { StorageService } from './storage.service';
import { filter, mapTo, switchMap, takeUntil, tap } from 'rxjs/operators';
import { MapiService } from './mapi.service';
import {
  AppSettings,
  BridgeLoadedMessageBody,
  DEFAULT_SETTINGS,
  HubMessage,
  isHubMessage,
  TYPES
} from '@skywind-group/lib-swui';
import { Meta } from '@angular/platform-browser';
import { environment } from '../environments/environment';
import { RefreshTokenService } from './refresh-token.service';

const TIME_INACTIVITY_LOGOUT = 900000;

function postMessage(type: string, body: any = '') {
  console.log('[bridge] postMessage', type, body);
  window.parent.postMessage({
    target: '*',
    initiator: 'bridge',
    type,
    body,
  }, '*');
}

function parseAppSettings(settings: object | string | null): AppSettings {
  if (typeof settings === 'object') {
    return settings as AppSettings;
  }
  if (typeof settings === 'string') {
    try {
      return JSON.parse(settings) || DEFAULT_SETTINGS;
    } catch (e) {
      console.warn(e);
    }
  }
  return DEFAULT_SETTINGS;
}

@Component({
  selector: 'sw-root',
  template: ''
})
export class AppComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly actionChanel: PubSub<string>;
  private readonly tokenChanel: PubSub<string>;
  private readonly permissionsChanel: PubSub<string>;
  private readonly twoFactorChanel: PubSub<boolean>;

  private readonly lastUserAction$ = new Subject<void>();
  private readonly destroyed$ = new Subject<void>();

  constructor(meta: Meta,
              private readonly storageService: StorageService,
              private readonly refreshService: RefreshTokenService,
              private readonly mapiService: MapiService
  ) {
    meta.addTag({ name: 'version', content: environment.APP_VERSION });
    this.actionChanel = new PubSub('action', type => {
      switch (type) {
        case TYPES.TOKEN:
          postMessage(TYPES.TOKEN, {
            accessToken: this.tokenChanel.get(),
            grantedPermissions: this.permissionsChanel.get(),
            twoFactor: this.twoFactorChanel.get()
          });
          break;
        case TYPES.LOGOUT:
          postMessage(type);
          break;
        case TYPES.USER_ACTIVITY:
          this.lastUserAction$.next();
          break;
        case TYPES.APP_SETTINGS_CHANGED:
        case TYPES.SIDEBAR_COLLAPSED:
        case TYPES.ENTITY_ID_CHANGED:
          this.storageService.getValue(type).then(value => {
            if (value) {
              postMessage(type, value);
            }
          });
          break;
        case TYPES.LOCALE_CHANGED:
          const language = localStorage.getItem('lang');
          if (language) {
            postMessage(type, language);
          }
          break;
      }
    });
    this.tokenChanel = new PubSub('token', token => {
      this.refreshService.setToken(token, this.permissionsChanel.get());
      this.storageService.setToken(token);
    });
    this.permissionsChanel = new PubSub('permissions');
    this.twoFactorChanel = new PubSub('two-factor');
    this.storageService.setToken(this.tokenChanel.get());
  }

  ngOnInit(): void {
    fromEvent<MessageEvent>(window, 'message').pipe(
      filter(event => isHubMessage(event)),
      filter(({ data }) => (data as HubMessage).target === 'bridge'),
      switchMap((event: MessageEvent<HubMessage>) => {
        const { type, body } = event.data;
        console.log('[bridge] getMessage', event.origin, type, body);
        switch (type) {
          case TYPES.TOKEN_EXPIRED: {
            return from(this.storageService.setValue(TYPES.LOCATION_CHANGED, null)).pipe(
              tap(() => {
                this.actionChanel.publish(TYPES.LOCATION_CHANGED);
                this.publishAccessToken(null, null, TYPES.LOGOUT);
              }),
              mapTo(event)
            );
          }
          case TYPES.LOGOUT: {
            return this.mapiService.logout(this.tokenChanel.get()).pipe(
              tap(() => {
                this.publishAccessToken(null, null, type);
              }),
              mapTo(event)
            );
          }
          case TYPES.LOGIN: {
            this.publishAccessToken(body.accessToken, body.grantedPermissions, TYPES.TOKEN, body.twoFactor);
            const locationData = localStorage.getItem(TYPES.UNKNOWN_LOCATION);
            if (!locationData) {
              return from(this.storageService.getValue(TYPES.LOCATION_CHANGED)).pipe(
                tap((value) => {
                  postMessage(TYPES.OPEN_HUB, value);
                }),
                mapTo(event)
              );
            }
            localStorage.removeItem(TYPES.UNKNOWN_LOCATION);
            const { initiator: hub, body: url } = JSON.parse(locationData);
            postMessage(TYPES.OPEN_HUB, { hub, url });
            break;
          }
          case TYPES.APP_SETTINGS_CHANGED:
          case TYPES.SIDEBAR_COLLAPSED:
          case TYPES.LOCATION_CHANGED:
          case TYPES.ENTITY_ID_CHANGED:
            return from(this.storageService.setValue(type, body)).pipe(
              tap(() => {
                this.actionChanel.publish(type);
              }),
              mapTo(event)
            );
        }
        return of(event);
      }),
      takeUntil(this.destroyed$)
    ).subscribe(({ data }) => {
      const { type, body } = data as HubMessage;
      switch (type) {
        case TYPES.UNKNOWN_LOCATION:
          localStorage.setItem(TYPES.UNKNOWN_LOCATION, JSON.stringify(data));
          break;
        case TYPES.USER_ACTIVITY:
          this.actionChanel.publish(type);
          break;
        case TYPES.TOKEN:
          this.publishAccessToken(body.accessToken, body.grantedPermissions, TYPES.TOKEN, body.twoFactor);
          break;
        case TYPES.LOCALE_CHANGED:
          localStorage.setItem('lang', body);
          this.actionChanel.publish(type);
          break;
      }
    });

    this.lastUserAction$.pipe(
      switchMap(() => timer(TIME_INACTIVITY_LOGOUT)),
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.publishAccessToken(null, null, TYPES.LOGOUT);
    });

    this.refreshService.token$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(({ token, permissions }) => {
      this.publishAccessToken(token, permissions, TYPES.TOKEN);
    });
    this.refreshService.setToken(this.tokenChanel.get(), this.permissionsChanel.get());
  }

  ngAfterViewInit(): void {
    Promise.all([
      this.storageService.getValue(TYPES.APP_SETTINGS_CHANGED),
      this.storageService.getValue(TYPES.LOCATION_CHANGED),
      this.storageService.getValue(TYPES.ENTITY_ID_CHANGED),
    ]).then(([settings, navigation, entityId]) => {
      const body: BridgeLoadedMessageBody = {
        accessToken: this.tokenChanel.get(),
        grantedPermissions: this.permissionsChanel.get(),
        twoFactor: this.twoFactorChanel.get(),
        lang: localStorage.getItem('lang') || 'en',
        settings: parseAppSettings(settings),
        navigation,
        entityId
      };
      postMessage(TYPES.BRIDGE_LOADED, body);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private publishAccessToken(accessToken: string | null, permissions: string | null, type: string, twoFactor?: boolean) {
    this.storageService.setToken(accessToken);
    this.tokenChanel.publish(accessToken);
    this.permissionsChanel.publish(permissions);
    this.actionChanel.publish(type);
    this.twoFactorChanel.publish(twoFactor);
  }
}

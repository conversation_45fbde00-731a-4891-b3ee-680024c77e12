import { BrowserModule } from '@angular/platform-browser';
import { APP_INITIALIZER, NgModule } from '@angular/core';

import { AppComponent } from './app.component';
import { StorageService } from './storage.service';
import { MapiService } from './mapi.service';
import { hub_config_init, SwHubConfigService } from '@skywind-group/lib-swui';
import { TranslateModule } from '@ngx-translate/core';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { RefreshTokenService } from './refresh-token.service';
import { HeadersInterceptor } from './headers-interceptor';

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    TranslateModule.forRoot()
  ],
  providers: [
    SwHubConfigService,
    { provide: APP_INITIALIZER, useFactory: hub_config_init, deps: [SwHubConfigService], multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: HeadersInterceptor, multi: true },
    StorageService,
    RefreshTokenService,
    MapiService
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
}

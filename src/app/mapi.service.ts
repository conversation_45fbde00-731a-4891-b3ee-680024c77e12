import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { take } from 'rxjs/operators';
import { environment } from '../environments/environment';
import { SwHubConfigService } from '@skywind-group/lib-swui';

export interface LoginInfo {
  key?: string;
  username: string;
  accessToken: string;
  grantedPermissions: string | null;
}

const API = environment.MAPI;

function headers( token: string ): any {
  return {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    'X-ACCESS-TOKEN': token
  };
}

@Injectable()
export class MapiService {
  private readonly logoutPath: string;

  constructor( { oauthClientId }: SwHubConfigService,
               private readonly http: HttpClient ) {
    this.logoutPath = oauthClientId ? '/oauth' : '';
  }

  refreshToken( token: string ): Observable<LoginInfo> {
    return this.http.post<LoginInfo>(`${API}/login/refresh`, {}, { headers: headers(token) }).pipe(
      take(1)
    );
  }

  logout( token: string ): Observable<void> {
    return this.http.post<void>(`${API}${this.logoutPath}/logout`, {}, { headers: headers(token) }).pipe(
      take(1)
    );
  }
}

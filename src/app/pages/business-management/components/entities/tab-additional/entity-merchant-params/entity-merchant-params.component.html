<div [formGroup]="form" class="entity-merchant-params">
  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="sameUrlForTerminalLoginAndTicket">
      {{'MERCHANT.PARAMETERS.sameUrlForTerminalLoginAndTicket' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row" *ngFor="let paramKey of filteredParamsKeys">
    <mat-form-field appearance="outline">
      <mat-label>{{ paramKey }}</mat-label>
      <input matInput [formControlName]="paramKey" type="text">
    </mat-form-field>
  </div>

  <div class="entity-merchant-params__footer" *ngIf="allowedEdit">
    <button mat-stroked-button color="primary" [disabled]="loading" (click)="onSubmit()">
      <i *ngIf="loading" class="icon-spinner4 spinner"></i>
      {{'ALL.save' | translate}}
    </button>
  </div>
</div>

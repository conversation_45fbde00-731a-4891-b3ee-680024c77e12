import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { finalize, map, switchMap, takeUntil, distinctUntilChanged } from 'rxjs/operators';
import { combineLatest } from 'rxjs';

import { Entity, UpdateMerchantEntityData } from '../../../../../../common/models/entity.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { MerchantTypesService, MerchantTypeSchema } from '../../../../../../common/services/merchant-types.service';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { PERMISSIONS_LIST } from '../../../../../../app.constants';


@Component({
  selector: 'entity-merchant-params',
  templateUrl: './entity-merchant-params.component.html',
  styleUrls: ['./entity-merchant-params.component.scss']
})

export class EntityMerchantParamsComponent implements OnInit {
  @Input('entity')
  set setWidgets( entity: Entity | undefined ) {
    this.path$.next(entity?.path);
  }

  readonly form: FormGroup;
  loading = false;
  public readonly allowedEdit: boolean;

  private entity: Entity;

  private readonly path$ = new BehaviorSubject<string | undefined>(undefined);
  private readonly entity$: Observable<Entity>;
  private readonly entityParams$: Observable<Entity['merchant']['params']>;
  private readonly typeSchema$: Observable<MerchantTypeSchema | undefined>;
  private readonly filteredParams$: Observable<{ [key: string]: any }>;
  private readonly destroyed$ = new Subject<void>();

  filteredParamsKeys: string[] = [];
  private currentTypeSchema: MerchantTypeSchema | undefined;

  constructor( private readonly entityService: EntityService<Entity>,
               private readonly merchantTypesService: MerchantTypesService,
               private readonly translate: TranslateService,
               private readonly notifications: SwuiNotificationsService,
               authService: SwHubAuthService,
  ) {
    this.allowedEdit = authService.allowedTo(PERMISSIONS_LIST.MERCHANT_EDIT);
    this.form = new FormGroup({
      sameUrlForTerminalLoginAndTicket: new FormControl({ value: '', disabled: !this.allowedEdit })
    });
    this.entity$ = this.path$.pipe(
      switchMap(path => this.entityService.getMerchantEntityItem(path)),
      map(entity => new Entity(entity))
    );
    this.entityParams$ = this.entity$.pipe(
      map(entity => entity?.merchant?.params ?? {})
    );
    this.typeSchema$ = this.entity$.pipe(
      switchMap(entity => {
        if (entity?.merchant?.type && entity?.path) {
          return this.merchantTypesService.get(entity.merchant.type, entity.path);
        }
        return [undefined];
      }),
      distinctUntilChanged()
    );
    this.filteredParams$ = combineLatest([this.entityParams$, this.typeSchema$]).pipe(
      map(([params, typeSchema]) => {
        const schemaKeys = typeSchema?.schema ? Object.keys(typeSchema.schema) : [];
        const filtered = { ...params };
        schemaKeys.forEach((key: string) => delete filtered[key]);
        return filtered;
      })
    );
  }

  ngOnInit() {
    this.entity$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(entity => {
      this.entity = entity;
      this.form.patchValue(entity?.merchant?.params ?? {});
    });

    this.filteredParams$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(filteredParams => {
      this.filteredParamsKeys = Object.keys(filteredParams);
      this.updateFormControls(filteredParams);
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private updateFormControls(filteredParams: { [key: string]: any }) {
    this.filteredParamsKeys.forEach(key => {
      if (!this.form.get(key)) {
        this.form.addControl(key, new FormControl({
          value: filteredParams[key] ?? '',
          disabled: !this.allowedEdit
        }));
      } else {
        this.form.get(key)?.setValue(filteredParams[key] ?? '');
      }
    });

    Object.keys(this.form.controls).forEach(controlKey => {
      if (controlKey !== 'sameUrlForTerminalLoginAndTicket' && !this.filteredParamsKeys.includes(controlKey)) {
        this.form.removeControl(controlKey);
      }
    });
  }

  onSubmit() {
    if (this.form.valid && !this.loading) {
      const updateData: UpdateMerchantEntityData = { ...this.entity.asUpdateMerchantData() };

      Object.keys(this.form.controls).forEach(key => {
        updateData.params[key] = this.form.get(key)?.value;
      });

      this.loading = true;
      this.entityService.updateMerchantEntityItem(updateData, this.path$.value).pipe(
        finalize(() => this.loading = false),
        switchMap(() => this.translate.get('INTEGRATIONS.notificationConfigSaved'))
      ).subscribe(message => {
        this.notifications.success(message, '');
      });
    }
  }
}

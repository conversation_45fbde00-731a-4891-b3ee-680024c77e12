import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { finalize, map, switchMap, takeUntil } from 'rxjs/operators';

import { Entity, UpdateMerchantEntityData } from '../../../../../../common/models/entity.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { PERMISSIONS_LIST } from '../../../../../../app.constants';


@Component({
  selector: 'entity-merchant-params',
  templateUrl: './entity-merchant-params.component.html',
  styleUrls: ['./entity-merchant-params.component.scss']
})

export class EntityMerchantParamsComponent implements OnInit {
  @Input('entity')
  set setWidgets( entity: Entity | undefined ) {
    this.path$.next(entity?.path);
  }

  readonly form: FormGroup;
  loading = false;
  public readonly allowedEdit: boolean;

  private entity: Entity;

  private readonly path$ = new BehaviorSubject<string | undefined>(undefined);
  private readonly entity$: Observable<Entity>;
  private readonly entityParams$: Observable<Entity['merchant']['params']>;
  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly entityService: EntityService<Entity>,
               private readonly translate: TranslateService,
               private readonly notifications: SwuiNotificationsService,
               authService: SwHubAuthService,
  ) {
    this.allowedEdit = authService.allowedTo(PERMISSIONS_LIST.MERCHANT_EDIT);
    this.form = new FormGroup({
      sameUrlForTerminalLoginAndTicket: new FormControl({ value: '', disabled: !this.allowedEdit })
    });
    this.entity$ = this.path$.pipe(
      switchMap(path => this.entityService.getMerchantEntityItem(path)),
      map(entity => new Entity(entity))
    );
    this.entityParams$ = this.entity$.pipe(
      map(entity => entity?.merchant?.params ?? {})
    );
  }

  ngOnInit() {
    this.entity$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(entity => {
      this.entity = entity;
      this.form.patchValue(entity?.merchant?.params ?? {});
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onSubmit() {
    if (this.form.valid && !this.loading) {
      const updateData: UpdateMerchantEntityData = { ...this.entity.asUpdateMerchantData() };
      updateData.params.sameUrlForTerminalLoginAndTicket = this.form.get('sameUrlForTerminalLoginAndTicket').value;
      this.loading = true;
      this.entityService.updateMerchantEntityItem(updateData, this.path$.value).pipe(
        finalize(() => this.loading = false),
        switchMap(() => this.translate.get('INTEGRATIONS.notificationConfigSaved'))
      ).subscribe(message => {
        this.notifications.success(message, '');
      });
    }
  }
}

import { Injectable } from '@angular/core';
import <PERSON>ie from 'dexie';
import { Subject } from 'rxjs';
import { take } from 'rxjs/operators';
import { JwtHelperService } from '@auth0/angular-jwt';
import { TYPES } from '@skywind-group/lib-swui';

interface StorageUser {
  username: string;
  entityId: number;
}

interface StorageValue {
  id?: number;
  username: string;
  entityId: number;
  value: any;
}

@Injectable()
export class StorageService extends Dexie {
  user?: StorageUser | null;
  private readonly ready$ = new Subject<StorageUser | null>();
  private readonly jwt = new JwtHelperService();

  constructor() {
    super('bridge');
    this.version(1).stores({
      [TYPES.LOCALE_CHANGED]: '++id, &[username+entityId], value',
      [TYPES.APP_SETTINGS_CHANGED]: '++id, &[username+entityId], value',
      [TYPES.SIDEBAR_COLLAPSED]: '++id, &[username+entityId], value',
      [TYPES.LOCATION_CHANGED]: '++id, &[username+entityId], value',
    });
    this.version(2).stores({
      [TYPES.ENTITY_ID_CHANGED]: '++id, &[username+entityId], value'
    });
  }

  setToken( accessToken: string | null ): void {
    const token = accessToken ? this.decodeToken(accessToken) : null;
    this.user = token ? { username: token.username, entityId: token.entityId } : null;
    this.ready$.next(this.user);
  }

  async getValue( tableName: string ): Promise<any | null> {
    const profile = await this.ready;
    if (!profile) {
      return Promise.resolve(null);
    }
    const table = this.table<StorageValue, number>(tableName);
    const row = await table.get(profile);
    return row ? row.value : null;
  }

  async setValue( tableName: string, value: any ): Promise<any> {
    const profile = await this.ready;
    if (!profile) {
      return Promise.resolve();
    }
    const table = this.table<StorageValue, number>(tableName);
    const row = await table.get(profile);
    if (row && row.id) {
      return table.update(row.id, { value });
    }
    return table.add({ ...profile, value });
  }

  private get ready(): Promise<StorageUser | null> {
    return typeof this.user !== 'undefined' ? Promise.resolve(this.user) : new Promise(resolve => {
      this.ready$.pipe(
        take(1)
      ).subscribe(user => {
        resolve(user);
      });
    });
  }

  private decodeToken( token: string ): any | null {
    try {
      return this.jwt.decodeToken(token);
    } catch (e) {
      console.warn(e);
    }
    return null;
  }
}

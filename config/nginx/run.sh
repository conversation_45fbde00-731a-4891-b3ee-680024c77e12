#!/bin/sh

if [ -z "${SOFTGATE_HOST}" ]; then
  export SOFTGATE_HOST='unknown'
fi

envsubst '$MAPI $BRIDGE_URL $LOGIN_URL $CASINO_HUB_URL $ENGAGEMENT_HUB_URL $DATA_HUB_URL $STUDIO_HUB_URL $SOFTGATE_HOST $SOFTGATE_BRIDGE_URL $SOFTGATE_LOGIN_URL $SOFTGATE_CASINO_HUB_URL $SOFTGATE_ENGAGEMENT_HUB_URL $SOFTGATE_DATA_HUB_URL $SOFTGATE_STUDIO_HUB_URL' < /usr/share/nginx/template.conf > /etc/nginx/conf.d/default.conf
nginx -g "daemon off;"
